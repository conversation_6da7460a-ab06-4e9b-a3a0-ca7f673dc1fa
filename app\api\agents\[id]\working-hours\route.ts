<<<<<<< HEAD
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { AvailabilityManager } from '@/lib/availability-utils';
import type { WorkHoursCreateInput } from '@/lib/types';

const availabilityManager = new AvailabilityManager();

interface Props {
  params: { id: string }
}

export async function GET(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Get agent's working hours
    const workingHours = await availabilityManager.getWorkingHours(agentId);

    return NextResponse.json({ workingHours });
  } catch (error) {
    console.error('Error in GET /api/agents/[id]/working-hours:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
=======
import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { createWorkingHoursManager } from "@/lib/working-hours-manager";
import { SetWorkingHoursRequest } from "@/lib/types";

/**
 * GET /api/agents/[id]/working-hours
 * Get agent's working hours schedule
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const agentId = params.id;
    const workingHoursManager = createWorkingHoursManager();

    // Check if user can access this agent's data
    if (session.user.role !== "ADMIN" && session.user.id !== agentId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const workingHours = await workingHoursManager.getAgentWorkingHours(agentId);

    return NextResponse.json({
      success: true,
      data: workingHours,
    });
  } catch (error) {
    console.error("Error fetching working hours:", error);
    return NextResponse.json(
      { error: "Failed to fetch working hours" },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
      { status: 500 }
    );
  }
}

<<<<<<< HEAD
export async function POST(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Parse request body
    const body = await req.json();
    const schedule: WorkHoursCreateInput[] = body.schedule;

    if (!Array.isArray(schedule)) {
      return NextResponse.json(
        { error: 'Schedule must be an array' },
=======
/**
 * POST /api/agents/[id]/working-hours
 * Set agent's working hours schedule
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const agentId = params.id;
    const workingHoursManager = createWorkingHoursManager();

    // Check if user can modify this agent's data
    if (session.user.role !== "ADMIN" && session.user.id !== agentId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body: SetWorkingHoursRequest = await request.json();

    // Validate request body
    if (!body.schedule || !Array.isArray(body.schedule)) {
      return NextResponse.json(
        { error: "Invalid schedule format" },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
        { status: 400 }
      );
    }

<<<<<<< HEAD
    // Set working hours
    const workingHours = await availabilityManager.setWorkingHours(
      agentId,
      schedule
    );

    return NextResponse.json(
      { workingHours },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/agents/[id]/working-hours:', error);
    
    // Handle validation errors
    if (error instanceof Error) {
      if (
        error.message.includes('Invalid day') ||
        error.message.includes('Invalid time') ||
        error.message.includes('Duplicate days')
      ) {
        return NextResponse.json(
          { error: error.message },
=======
    // Validate each schedule entry
    for (const day of body.schedule) {
      if (
        typeof day.dayOfWeek !== "number" ||
        day.dayOfWeek < 0 ||
        day.dayOfWeek > 6 ||
        typeof day.startTime !== "string" ||
        typeof day.endTime !== "string" ||
        !day.startTime.match(/^\d{2}:\d{2}$/) ||
        !day.endTime.match(/^\d{2}:\d{2}$/)
      ) {
        return NextResponse.json(
          { error: "Invalid schedule entry format" },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
          { status: 400 }
        );
      }
    }

<<<<<<< HEAD
    return NextResponse.json(
      { error: 'Internal server error' },
=======
    const workingHours = await workingHoursManager.setAgentWorkingHours(
      agentId,
      body.schedule
    );

    return NextResponse.json({
      success: true,
      data: workingHours,
      message: "Working hours updated successfully",
    });
  } catch (error) {
    console.error("Error setting working hours:", error);
    return NextResponse.json(
      { error: "Failed to set working hours" },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
      { status: 500 }
    );
  }
}

<<<<<<< HEAD
export async function PATCH(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Parse request body
    const body = await req.json();
    const { dayOfWeek, updates } = body;

    if (typeof dayOfWeek !== 'number' || !updates) {
      return NextResponse.json(
        { error: 'Invalid request format' },
=======
/**
 * PATCH /api/agents/[id]/working-hours
 * Update specific day working hours
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const agentId = params.id;
    const workingHoursManager = createWorkingHoursManager();

    // Check if user can modify this agent's data
    if (session.user.role !== "ADMIN" && session.user.id !== agentId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const { dayOfWeek, startTime, endTime, timezone } = body;

    // Validate request body
    if (
      typeof dayOfWeek !== "number" ||
      dayOfWeek < 0 ||
      dayOfWeek > 6 ||
      typeof startTime !== "string" ||
      typeof endTime !== "string" ||
      !startTime.match(/^\d{2}:\d{2}$/) ||
      !endTime.match(/^\d{2}:\d{2}$/)
    ) {
      return NextResponse.json(
        { error: "Invalid request format" },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
        { status: 400 }
      );
    }

<<<<<<< HEAD
    // Get current schedule
    const currentSchedule = await availabilityManager.getWorkingHours(agentId);
    
    // Update specific day
    const updatedSchedule = currentSchedule.map(day => {
      if (day.dayOfWeek === dayOfWeek) {
        return {
          ...day,
          ...updates,
          dayOfWeek // Ensure dayOfWeek can't be changed
        };
      }
      return day;
    });

    // Set updated schedule
    const workingHours = await availabilityManager.setWorkingHours(
      agentId,
      updatedSchedule.map(day => ({
        dayOfWeek: day.dayOfWeek,
        startTime: day.startTime,
        endTime: day.endTime,
        timeZone: day.timeZone,
        isWorkDay: day.isWorkDay
      }))
    );

    return NextResponse.json({ workingHours });
  } catch (error) {
    console.error('Error in PATCH /api/agents/[id]/working-hours:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
=======
    const workingHours = await workingHoursManager.updateWorkingDay(
      agentId,
      dayOfWeek,
      startTime,
      endTime,
      timezone
    );

    return NextResponse.json({
      success: true,
      data: workingHours,
      message: "Working day updated successfully",
    });
  } catch (error) {
    console.error("Error updating working day:", error);
    return NextResponse.json(
      { error: "Failed to update working day" },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
      { status: 500 }
    );
  }
}
<<<<<<< HEAD

export async function DELETE(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Delete all working hours (reset schedule)
    await availabilityManager.setWorkingHours(agentId, []);

    return NextResponse.json(
      { message: 'Working hours reset successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in DELETE /api/agents/[id]/working-hours:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
=======
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
