<<<<<<< HEAD
import { NextRequest, NextResponse } from 'next/server';
import { AvailabilityManager } from '../../../../../lib/availability-utils';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '../../../../../lib/prisma';
import { UserStatus } from '@prisma/client';

const availabilityManager = new AvailabilityManager();

interface Props {
  params: { id: string }
}

export async function GET(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Get current availability
    const currentAvailability = await availabilityManager.getCurrentAvailability(agentId);

    // Get scheduled changes
    const scheduledChanges = await prisma.agentAvailability.findMany({
      where: {
        agentId,
        isScheduled: true,
        endTime: {
          gt: new Date()
        }
      },
      orderBy: {
        startTime: 'asc'
      }
    });

    // Check if agent is within working hours
    const isWithinWorkingHours = await availabilityManager.isAgentAvailable(agentId);

    return NextResponse.json({
      currentAvailability,
      scheduledChanges,
      isWithinWorkingHours
    });
  } catch (error) {
    console.error('Error in GET /api/agents/[id]/availability:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
=======
import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { createWorkingHoursManager } from "@/lib/working-hours-manager";
import { SetAvailabilityRequest } from "@/lib/types";

/**
 * GET /api/agents/[id]/availability
 * Get agent's current availability status
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const agentId = params.id;
    const workingHoursManager = createWorkingHoursManager();

    // Check if user can access this agent's data
    if (session.user.role !== "ADMIN" && session.user.id !== agentId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const availability = await workingHoursManager.getAgentAvailability(agentId);
    const isAvailable = await workingHoursManager.isAgentAvailable(agentId);

    return NextResponse.json({
      success: true,
      data: {
        availability,
        isAvailable,
      },
    });
  } catch (error) {
    console.error("Error fetching availability:", error);
    return NextResponse.json(
      { error: "Failed to fetch availability" },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
      { status: 500 }
    );
  }
}

<<<<<<< HEAD
export async function POST(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Parse request body
    const body = await req.json();
    const { 
      status, 
      statusMessage,
      startTime,
      endTime
    } = body;

    // Validate status
    if (!Object.values(UserStatus).includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
=======
/**
 * POST /api/agents/[id]/availability
 * Set agent's availability status
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const agentId = params.id;
    const workingHoursManager = createWorkingHoursManager();

    // Check if user can modify this agent's data
    if (session.user.role !== "ADMIN" && session.user.id !== agentId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body: SetAvailabilityRequest = await request.json();

    // Validate request body
    const validStatuses = ["available", "busy", "away", "offline"];
    if (!body.status || !validStatuses.includes(body.status)) {
      return NextResponse.json(
        { error: "Invalid status. Must be one of: " + validStatuses.join(", ") },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
        { status: 400 }
      );
    }

<<<<<<< HEAD
    // Handle scheduled availability
    if (startTime && endTime) {
      try {
        const scheduledAvailability = await availabilityManager.scheduleAvailability(
          agentId,
          status,
          new Date(startTime),
          new Date(endTime),
          statusMessage
        );

        return NextResponse.json(
          { availability: scheduledAvailability },
          { status: 201 }
        );
      } catch (error) {
        if (error instanceof Error && error.message === 'Schedule conflict detected') {
          return NextResponse.json(
            { error: 'Schedule conflict detected' },
            { status: 409 }
          );
        }
        throw error;
      }
    }

    // Update current availability
    const availability = await availabilityManager.updateAvailability(
      agentId,
      status,
      { statusMessage }
    );

    return NextResponse.json(
      { availability },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/agents/[id]/availability:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
=======
    if (body.duration && (typeof body.duration !== "number" || body.duration <= 0)) {
      return NextResponse.json(
        { error: "Duration must be a positive number (minutes)" },
        { status: 400 }
      );
    }

    const availability = await workingHoursManager.setAgentAvailability(
      agentId,
      body.status,
      body.reason,
      body.duration
    );

    return NextResponse.json({
      success: true,
      data: availability,
      message: "Availability status updated successfully",
    });
  } catch (error) {
    console.error("Error setting availability:", error);
    return NextResponse.json(
      { error: "Failed to set availability" },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
      { status: 500 }
    );
  }
}

<<<<<<< HEAD
export async function DELETE(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const agentId = params.id;
    
    // Get schedule ID from query params
    const { searchParams } = new URL(req.url);
    const scheduleId = searchParams.get('scheduleId');

    if (!scheduleId) {
      return NextResponse.json(
        { error: 'Schedule ID is required' },
=======
/**
 * PATCH /api/agents/[id]/availability
 * Update availability with duration
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const agentId = params.id;
    const workingHoursManager = createWorkingHoursManager();

    // Check if user can modify this agent's data
    if (session.user.role !== "ADMIN" && session.user.id !== agentId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const { startTime, endTime, reason } = body;

    // Validate request body
    if (!startTime || !endTime || !reason) {
      return NextResponse.json(
        { error: "startTime, endTime, and reason are required" },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
        { status: 400 }
      );
    }

<<<<<<< HEAD
    // Delete scheduled availability
    await prisma.agentAvailability.deleteMany({
      where: {
        id: scheduleId,
        agentId,
        isScheduled: true,
        startTime: {
          gt: new Date()
        }
      }
    });

    return NextResponse.json(
      { message: 'Scheduled availability deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in DELETE /api/agents/[id]/availability:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
=======
    const startDate = new Date(startTime);
    const endDate = new Date(endTime);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: "Invalid date format" },
        { status: 400 }
      );
    }

    if (endDate <= startDate) {
      return NextResponse.json(
        { error: "End time must be after start time" },
        { status: 400 }
      );
    }

    const availability = await workingHoursManager.setTemporaryUnavailability(
      agentId,
      startDate,
      endDate,
      reason
    );

    return NextResponse.json({
      success: true,
      data: availability,
      message: "Temporary unavailability set successfully",
    });
  } catch (error) {
    console.error("Error setting temporary unavailability:", error);
    return NextResponse.json(
      { error: "Failed to set temporary unavailability" },
      { status: 500 }
    );
  }
}
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
