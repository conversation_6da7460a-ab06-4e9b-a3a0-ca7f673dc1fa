<<<<<<< HEAD
import { prisma } from './prisma';
import { SkillsManager } from './skills-utils';
import { RulesEngine } from './rules-engine';
import { SLAManager } from './sla-manager';
import { UserStatus } from '@prisma/client';
import {
  TaskWithSkills,
  AgentWithSkills,
  RoutingResult,
  RoutingContext,
  RuleExecutionResult,
  SLAStatus
} from './types';

export class EnhancedTaskRouter {
  private skillsManager: SkillsManager;
  private rulesEngine: RulesEngine;
  private slaManager: SLAManager;

  constructor() {
    this.skillsManager = new SkillsManager();
    this.rulesEngine = new RulesEngine();
    this.slaManager = new SLAManager();
  }

  async routeTask(context: RoutingContext): Promise<RoutingResult> {
    const { task, strategy = 'weighted_round_robin' } = context;

    try {
      // Check SLA status first
      const slaStatus = task.slaPolicyId 
        ? await this.slaManager.checkSLAStatus(task.id)
        : null;

      // Apply routing rules
      const ruleResults = await this.rulesEngine.evaluateRules({
        task,
        organizationId: task.organizationId,
        slaStatus
      });

      // Update task metadata with rule results
      await this.updateTaskWithRuleResults(task.id, ruleResults);

      // Get modified routing strategy
      const modifiedStrategy = this.getModifiedStrategy(ruleResults, slaStatus) || strategy;

      // Get eligible agents considering rules and SLA
      const eligibleAgents = await this.getEligibleAgents(task, ruleResults, slaStatus);
      
      if (eligibleAgents.length === 0) {
        throw new Error('No eligible agents available');
      }

      // Score agents considering SLA
      const agentScores = await Promise.all(
        eligibleAgents.map(async (agent) => ({
          agent,
          score: await this.calculateAgentScore(agent, task, slaStatus),
          reasoning: [`Base agent scoring with SLA considerations`]
        }))
      );

      // Sort by score and get best match
      const sortedScores = agentScores.sort((a, b) => b.score - a.score);
      const selectedScore = sortedScores[0];

      return {
        selectedAgent: selectedScore.agent,
        confidence: selectedScore.score,
        reasoning: selectedScore.reasoning,
        alternativeAgents: sortedScores.slice(1, 4).map(score => score.agent),
        ruleResults,
        appliedStrategy: modifiedStrategy,
        slaStatus
      };

    } catch (error) {
      console.error('Error in routeTask:', error);
      throw error;
    }
  }

  private async updateTaskWithRuleResults(taskId: string, ruleResults: RuleExecutionResult[]): Promise<void> {
    await prisma.task.update({
      where: { id: taskId },
      data: {
        metadata: {
          ruleResults: ruleResults.map(result => ({
            ruleId: result.ruleId,
            ruleName: result.ruleName,
            succeeded: result.succeeded,
            actionsExecuted: result.actionsExecuted
          }))
        }
      }
    });
  }

  private getModifiedStrategy(
    ruleResults: RuleExecutionResult[],
    slaStatus: SLAStatus | null
  ): string | undefined {
    // If SLA is breached or close to breach, prioritize performance-based routing
    if (slaStatus?.breachedSLA || this.isNearingSLABreach(slaStatus)) {
      return 'performance_based';
    }

    // Check for strategy modifications from rules
    const strategyRule = ruleResults.find(result => 
      result.succeeded && 
      result.actionsExecuted?.some(action => action.includes('set_strategy'))
    );

    return strategyRule?.actionsExecuted?.find(action => 
      action.startsWith('set_strategy:')
    )?.split(':')[1];
  }

  private async getEligibleAgents(
    task: TaskWithSkills,
    ruleResults: RuleExecutionResult[],
    slaStatus: SLAStatus | null
  ): Promise<AgentWithSkills[]> {
    // Get base eligible agents
    const dbAgents = await prisma.user.findMany({
      where: {
        organizationId: task.organizationId,
        role: 'AGENT',
        status: UserStatus.AVAILABLE,
        currentTaskCount: {
          lt: prisma.user.fields.maxConcurrentTasks
        }
      },
      include: {
        skills: {
          select: {
            id: true,
            agentId: true,
            skillName: true,
            proficiencyLevel: true,
            createdAt: true
          }
        },
        performance: {
          select: {
            id: true,
            agentId: true,
            avgResponseTime: true,
            avgResolutionTime: true,
            completionRate: true,
            qualityScore: true,
            totalTasksCompleted: true,
            totalTasksAssigned: true,
            lastUpdated: true
          }
        }
      }
    });

    // Transform database results to AgentWithSkills type
    let agents = dbAgents.map(agent => ({
      ...agent,
      skills: agent.skills || [],
      currentTaskCount: agent.currentTaskCount || 0,
      maxConcurrentTasks: agent.maxConcurrentTasks || 5 // Default if not set
    })) as AgentWithSkills[];

    // Apply rule-based filters
    agents = this.applyRuleFilters(agents, ruleResults);

    // Apply SLA-based filters if close to breach
    if (this.isNearingSLABreach(slaStatus)) {
      agents = agents.filter(agent => {
        const performance = agent.performance;
        if (!performance) return false;
        
        // Filter out agents with poor performance metrics
        const minCompletionRate = 0.7; // 70%
        const maxResponseTime = 30; // 30 minutes
        const maxResolutionTime = 240; // 4 hours

        return (
          performance.completionRate >= minCompletionRate &&
          performance.avgResponseTime <= maxResponseTime &&
          performance.avgResolutionTime <= maxResolutionTime
        );
      });
    }

    return agents;
  }

  private applyRuleFilters(
    agents: AgentWithSkills[],
    ruleResults: RuleExecutionResult[]
  ): AgentWithSkills[] {
    let filteredAgents = [...agents];

    for (const result of ruleResults) {
      if (!result.succeeded) continue;

      const performanceRule = result.actionsExecuted?.find(action =>
        action.startsWith('require_min_performance:')
      );

      if (performanceRule) {
        const minScore = parseFloat(performanceRule.split(':')[1]);
        filteredAgents = filteredAgents.filter(agent => {
          const qualityScore = agent.performance?.qualityScore ?? 0;
          return qualityScore >= minScore;
        });
      }
    }

    return filteredAgents;
  }

  private async calculateAgentScore(
    agent: AgentWithSkills,
    task: TaskWithSkills,
    slaStatus: SLAStatus | null
  ): Promise<number> {
    let score = 0;
    
    // Base skill match score (50% weight)
    const skillMatchScore = await this.skillsManager.calculateSkillMatch(
      agent.skills,
      task.requiredSkills
    );
    score += skillMatchScore * 0.5;

    // Workload score (20% weight)
    const workloadScore = 1 - (agent.currentTaskCount / agent.maxConcurrentTasks);
    score += workloadScore * 0.2;

    // Performance score with SLA considerations (30% weight)
    if (agent.performance) {
      const performanceScore = this.calculatePerformanceScore(agent, slaStatus);
      score += performanceScore * 0.3;
    }

    return Math.max(0, Math.min(1, score));
  }

  private calculatePerformanceScore(
    agent: AgentWithSkills,
    slaStatus: SLAStatus | null
  ): number {
    if (!agent.performance) return 0;

    const {
      completionRate,
      avgResponseTime,
      avgResolutionTime,
      qualityScore
    } = agent.performance;

    let score = 0;

    // Base performance metrics
    score += completionRate * 0.3;
    score += qualityScore * 0.3;

    // Response time score (normalized to 0-1, assuming 60 min max)
    const responseScore = Math.max(0, 1 - (avgResponseTime / 60));
    score += responseScore * 0.2;

    // Resolution time score (normalized to 0-1, assuming 8 hours max)
    const resolutionScore = Math.max(0, 1 - (avgResolutionTime / 480));
    score += resolutionScore * 0.2;

    // Apply SLA-based modifiers
    if (slaStatus) {
      if (this.isNearingSLABreach(slaStatus, 'response')) {
        score *= (1 + responseScore);
      }
      if (this.isNearingSLABreach(slaStatus, 'resolution')) {
        score *= (1 + resolutionScore);
      }
    }

    return Math.max(0, Math.min(1, score));
  }

  private isNearingSLABreach(
    slaStatus: SLAStatus | null,
    type: 'response' | 'resolution' | 'both' = 'both'
  ): boolean {
    if (!slaStatus) return false;

    const warningThreshold = 0.25; // 25% of time remaining

    if (type === 'response' || type === 'both') {
      if (slaStatus.responseRemaining !== null) {
        const responseProgress = slaStatus.responseRemaining / (slaStatus.responseRemaining + 1);
        if (responseProgress < warningThreshold) return true;
      }
    }

    if (type === 'resolution' || type === 'both') {
      if (slaStatus.resolutionRemaining !== null) {
        const resolutionProgress = slaStatus.resolutionRemaining / (slaStatus.resolutionRemaining + 1);
        if (resolutionProgress < warningThreshold) return true;
      }
    }

    return false;
  }
}
=======
import { prisma } from './prisma'
import type { 
  RoutingContext, 
  RoutingResult, 
  AgentWithSkills, 
  TaskWithSkills,
  ScoredAgent,
  EnhancedRoutingStrategy,
  AgentWithPerformance
} from './types'
import type { AgentSkill, TaskRequiredSkill } from '@prisma/client'

// ============================================================================
// ENHANCED TASK ROUTER CLASS
// ============================================================================

export class EnhancedTaskRouter {
  private organizationId: string

  constructor(organizationId: string) {
    this.organizationId = organizationId
  }

  /**
   * Main enhanced routing method
   */
  async routeTask(context: RoutingContext): Promise<RoutingResult> {
    try {
      const strategy = context.strategy || await this.getDefaultStrategy(context.organizationId)
      
      switch (strategy) {
        case 'weighted_round_robin':
          return this.weightedRoundRobin(context)
        case 'best_skill_match':
          return this.bestSkillMatch(context)
        case 'performance_based':
          return this.performanceBased(context)
        case 'hybrid_intelligent':
          return this.hybridIntelligent(context)
        default:
          return this.weightedRoundRobin(context)
      }
    } catch (error) {
      console.error('Enhanced routing error:', error)
      // Fallback to first available agent
      return this.fallbackRouting(context)
    }
  }

  /**
   * Enhanced weighted round robin with skill matching
   */
  async weightedRoundRobin(context: RoutingContext): Promise<RoutingResult> {
    const { task, availableAgents } = context
    const reasoning: string[] = ['Using weighted round robin strategy']

    const scoredAgents = availableAgents.map(agent => {
      const workloadScore = this.calculateWorkloadScore(agent)
      const skillScore = this.calculateSkillMatchScore(agent.skills, task.requiredSkills)
      const performanceScore = this.calculatePerformanceScore(agent)
      
      const totalScore = (workloadScore * 0.4) + (skillScore * 0.4) + (performanceScore * 0.2)
      
      return {
        agent,
        score: totalScore,
        breakdown: {
          skillScore,
          workloadScore,
          performanceScore
        }
      }
    })

    reasoning.push(`Evaluated ${scoredAgents.length} agents with workload (40%), skills (40%), performance (20%) weighting`)

    return this.selectByWeight(scoredAgents, task, reasoning)
  }

  /**
   * Best skill match strategy - prioritizes skill matching
   */
  async bestSkillMatch(context: RoutingContext): Promise<RoutingResult> {
    const { task, availableAgents } = context
    const reasoning: string[] = ['Using best skill match strategy']

    const scoredAgents = availableAgents.map(agent => {
      const skillScore = this.calculateSkillMatchScore(agent.skills, task.requiredSkills)
      const workloadScore = this.calculateWorkloadScore(agent)
      
      const totalScore = (skillScore * 0.8) + (workloadScore * 0.2)
      
      return {
        agent,
        score: totalScore,
        breakdown: {
          skillScore,
          workloadScore,
          performanceScore: 0
        }
      }
    })

    reasoning.push(`Prioritized skill matching (80%) over workload (20%)`)

    return this.selectBestScore(scoredAgents, task, reasoning)
  }

  /**
   * Performance-based strategy - prioritizes historical performance
   */
  async performanceBased(context: RoutingContext): Promise<RoutingResult> {
    const { task, availableAgents } = context
    const reasoning: string[] = ['Using performance-based strategy']

    const scoredAgents = availableAgents.map(agent => {
      const performanceScore = this.calculatePerformanceScore(agent)
      const skillScore = this.calculateSkillMatchScore(agent.skills, task.requiredSkills)
      const workloadScore = this.calculateWorkloadScore(agent)
      
      const totalScore = (performanceScore * 0.5) + (skillScore * 0.3) + (workloadScore * 0.2)
      
      return {
        agent,
        score: totalScore,
        breakdown: {
          skillScore,
          workloadScore,
          performanceScore
        }
      }
    })

    reasoning.push(`Prioritized performance (50%), skills (30%), workload (20%)`)

    return this.selectBestScore(scoredAgents, task, reasoning)
  }

  /**
   * Hybrid intelligent strategy - dynamic weighting based on task characteristics
   */
  async hybridIntelligent(context: RoutingContext): Promise<RoutingResult> {
    const { task, availableAgents } = context
    const reasoning: string[] = ['Using hybrid intelligent strategy']
    const urgencyWeight = this.calculateUrgencyWeight(task)

    // Dynamic weighting based on urgency and task characteristics
    let skillWeight = 0.4
    let performanceWeight = 0.3
    let workloadWeight = 0.3

    if (urgencyWeight > 0.8) {
      // High urgency: prioritize performance and availability
      performanceWeight = 0.5
      workloadWeight = 0.4
      skillWeight = 0.1
      reasoning.push('High urgency detected - prioritizing performance and availability')
    } else if (task.requiredSkills.length > 0) {
      // Skill-specific task: prioritize skill match
      skillWeight = 0.6
      performanceWeight = 0.2
      workloadWeight = 0.2
      reasoning.push('Skill requirements detected - prioritizing skill matching')
    }

    const scoredAgents = availableAgents.map(agent => {
      const skillScore = this.calculateSkillMatchScore(agent.skills, task.requiredSkills)
      const workloadScore = this.calculateWorkloadScore(agent)
      const performanceScore = this.calculatePerformanceScore(agent)

      const totalScore = (skillScore * skillWeight) +
                        (performanceScore * performanceWeight) +
                        (workloadScore * workloadWeight)

      return {
        agent,
        score: totalScore,
        breakdown: {
          skillScore,
          workloadScore,
          performanceScore,
          urgencyScore: urgencyWeight
        }
      }
    })

    reasoning.push(`Dynamic weighting: skills (${Math.round(skillWeight * 100)}%), performance (${Math.round(performanceWeight * 100)}%), workload (${Math.round(workloadWeight * 100)}%)`)

    return this.selectByWeight(scoredAgents, task, reasoning)
  }

  /**
   * Calculate skill match score (0-1)
   */
  calculateSkillMatchScore(agentSkills: AgentSkill[], requiredSkills: TaskRequiredSkill[]): number {
    if (requiredSkills.length === 0) return 1.0 // No requirements = perfect match
    
    let totalMatch = 0
    let totalRequired = 0
    
    for (const required of requiredSkills) {
      const agentSkill = agentSkills.find(s => s.skillName === required.skillName)
      
      if (agentSkill) {
        // Calculate match quality based on proficiency vs requirement
        const matchQuality = Math.min(agentSkill.proficiencyLevel / required.requiredLevel, 1.0)
        totalMatch += matchQuality
      }
      totalRequired += 1
    }
    
    return totalMatch / totalRequired
  }

  /**
   * Calculate workload score (0-1, higher is better)
   */
  calculateWorkloadScore(agent: AgentWithSkills): number {
    const utilization = agent.currentTaskCount / agent.maxConcurrentTasks
    return Math.max(0, 1 - utilization) // Higher score for lower utilization
  }

  /**
   * Calculate performance score (0-1)
   */
  calculatePerformanceScore(agent: AgentWithSkills): number {
    const agentWithPerf = agent as AgentWithPerformance
    if (!agentWithPerf.performance) return 0.5 // Default score for new agents
    
    const completionScore = agentWithPerf.performance.completionRate
    const qualityScore = agentWithPerf.performance.qualityScore / 100
    const responseScore = Math.max(0, 1 - (agentWithPerf.performance.avgResponseTime / 60)) // Normalize to hours
    
    return (completionScore * 0.4) + (qualityScore * 0.4) + (responseScore * 0.2)
  }

  /**
   * Calculate urgency weight based on task priority
   */
  calculateUrgencyWeight(task: TaskWithSkills): number {
    switch (task.priority) {
      case 'URGENT': return 1.0
      case 'HIGH': return 0.8
      case 'MEDIUM': return 0.5
      case 'LOW': return 0.2
      default: return 0.5
    }
  }

  /**
   * Get default routing strategy for organization
   */
  private async getDefaultStrategy(organizationId: string): Promise<EnhancedRoutingStrategy> {
    const defaultStrategy = await prisma.routing_strategies.findFirst({
      where: {
        organization_id: organizationId,
        is_default: true,
        is_active: true
      }
    })
    
    return (defaultStrategy?.strategy as EnhancedRoutingStrategy) || 'weighted_round_robin'
  }

  /**
   * Select agent by weighted random selection
   */
  private selectByWeight(scoredAgents: ScoredAgent[], task: TaskWithSkills, reasoning: string[]): RoutingResult {
    if (scoredAgents.length === 0) {
      throw new Error('No agents available for selection')
    }

    // Sort by score for alternatives
    const sortedAgents = [...scoredAgents].sort((a, b) => b.score - a.score)
    
    // Weighted random selection
    const totalWeight = scoredAgents.reduce((sum, sa) => sum + sa.score, 0)
    let random = Math.random() * totalWeight
    
    let selectedAgent = sortedAgents[0].agent // fallback
    for (const { agent, score } of scoredAgents) {
      random -= score
      if (random <= 0) {
        selectedAgent = agent
        break
      }
    }

    const selectedScore = scoredAgents.find(sa => sa.agent.id === selectedAgent.id)?.score || 0
    const confidence = Math.round(selectedScore * 100)
    
    reasoning.push(`Selected agent with confidence score: ${confidence}%`)
    
    return {
      selectedAgent,
      confidence,
      reasoning,
      alternativeAgents: sortedAgents.slice(0, 3).map(sa => sa.agent).filter(a => a.id !== selectedAgent.id)
    }
  }

  /**
   * Select agent with best score
   */
  private selectBestScore(scoredAgents: ScoredAgent[], task: TaskWithSkills, reasoning: string[]): RoutingResult {
    if (scoredAgents.length === 0) {
      throw new Error('No agents available for selection')
    }

    const sortedAgents = [...scoredAgents].sort((a, b) => b.score - a.score)
    const selectedAgent = sortedAgents[0].agent
    const confidence = Math.round(sortedAgents[0].score * 100)
    
    reasoning.push(`Selected highest scoring agent with confidence: ${confidence}%`)
    
    return {
      selectedAgent,
      confidence,
      reasoning,
      alternativeAgents: sortedAgents.slice(1, 4).map(sa => sa.agent)
    }
  }

  /**
   * Fallback routing when enhanced routing fails
   */
  private fallbackRouting(context: RoutingContext): RoutingResult {
    const { availableAgents } = context
    
    if (availableAgents.length === 0) {
      throw new Error('No agents available for fallback routing')
    }

    return {
      selectedAgent: availableAgents[0],
      confidence: 50,
      reasoning: ['Fallback routing - selected first available agent'],
      alternativeAgents: availableAgents.slice(1, 3)
    }
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create an enhanced task router instance
 */
export function createEnhancedTaskRouter(organizationId: string): EnhancedTaskRouter {
  return new EnhancedTaskRouter(organizationId)
}
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
