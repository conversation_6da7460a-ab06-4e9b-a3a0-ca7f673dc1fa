import { prisma } from './prisma'
<<<<<<< HEAD
import { UserStatus, TaskStatus, TaskEventType } from '@prisma/client'
import type { User } from '@prisma/client'
import { PerformanceManager } from './performance-manager'
=======
import { TaskStatus, TaskEventType } from '@prisma/client'
import type { User } from '@prisma/client'
import { createSkillsManager } from './skills-utils'
import type { AgentSkillMatch } from './types'
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a

// ============================================================================
// TYPES
// ============================================================================

export interface RoutingResult {
  success: boolean
  assignedAgent?: User
  reason?: string
  error?: string
}

export interface EligibleAgent extends User {
  utilizationRate: number
  lastAssignedAt?: Date
  skillMatch?: number // percentage 0-100
  missingSkills?: string[]
}

export type RoutingStrategy = 'round_robin' | 'least_loaded' | 'weighted_round_robin' | 'skill_based'

// ============================================================================
// TASK ROUTER CLASS
// ============================================================================

export class TaskRouter {
  private organizationId: string
  private performanceManager: PerformanceManager

  constructor(organizationId: string) {
    this.organizationId = organizationId
    this.performanceManager = new PerformanceManager()
  }

  /**
   * Main routing function - assigns a task to the best available agent
   */
  async routeTask(taskId: string, strategy: RoutingStrategy = 'round_robin'): Promise<RoutingResult> {
    try {
      // Get the task with skill requirements
      const task = await prisma.task.findUnique({
        where: { id: taskId },
        include: {
          organization: true,
          requiredSkills: true
        }
      })

      if (!task) {
        return { success: false, error: 'Task not found' }
      }

      if (task.assignedTo) {
        return { success: false, reason: 'Task already assigned' }
      }

      // Get eligible agents (considering skills if required)
      const eligibleAgents = await this.getEligibleAgents(taskId)

      if (eligibleAgents.length === 0) {
        return { success: false, reason: 'No eligible agents available' }
      }

      // Select agent based on strategy
      const selectedAgent = await this.selectAgent(eligibleAgents, strategy)

      if (!selectedAgent) {
        return { success: false, reason: 'No suitable agent found' }
      }

      // Assign the task
      const assignmentResult = await this.assignTaskToAgent(taskId, selectedAgent.id)

      if (assignmentResult.success) {
        return {
          success: true,
          assignedAgent: selectedAgent,
          reason: `Assigned using ${strategy} strategy`
        }
      } else {
        return assignmentResult
      }

    } catch (error) {
      console.error('Error in task routing:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown routing error' 
      }
    }
  }

  /**
   * Get all eligible agents for task assignment (considering skills)
   */
  async getEligibleAgents(taskId?: string): Promise<EligibleAgent[]> {
    // Get all agents with their skills (we'll filter by availability later)
    const agents = await prisma.user.findMany({
      where: {
        organizationId: this.organizationId,
      },
      include: {
        skills: true,
        availability: {
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      },
    })

    // Get task skill requirements if taskId provided
    let skillMatches: AgentSkillMatch[] = []
    if (taskId) {
      const skillsManager = createSkillsManager()
      skillMatches = await skillsManager.getAgentSkillMatches(taskId)
    }

    // Filter agents with available capacity and calculate metrics
    const eligibleAgents: EligibleAgent[] = []

    for (const agent of agents) {
      // Check availability status first
      const currentAvailability = agent.availability[0]
      if (currentAvailability && currentAvailability.status !== 'available') {
        // Check if temporary status has expired
        if (currentAvailability.isTemporary && currentAvailability.endTime && new Date() > currentAvailability.endTime) {
          // Status has expired, agent is available
        } else {
          // Agent is not available
          continue
        }
      }

      // Check if agent has capacity
      if (agent.currentTaskCount >= agent.maxConcurrentTasks) {
        continue
      }

      // Calculate utilization rate
      const utilizationRate = agent.maxConcurrentTasks > 0
        ? (agent.currentTaskCount / agent.maxConcurrentTasks) * 100
        : 0

      // Get last assignment time for round-robin
      const lastAssignment = await prisma.task.findFirst({
        where: {
          assignedTo: agent.id,
          assignedAt: { not: null }
        },
        orderBy: { assignedAt: 'desc' },
        select: { assignedAt: true }
      })

      // Find skill match data for this agent
      const agentSkillMatch = skillMatches.find(match => match.agent.id === agent.id)

      eligibleAgents.push({
        ...agent,
        utilizationRate,
        lastAssignedAt: lastAssignment?.assignedAt || undefined,
        skillMatch: agentSkillMatch?.overallMatch || 100, // 100% if no skills required
        missingSkills: agentSkillMatch?.missingSkills || []
      })
    }

    return eligibleAgents
  }

  /**
   * Select the best agent based on routing strategy
   */
  private async selectAgent(
    agents: EligibleAgent[], 
    strategy: RoutingStrategy
  ): Promise<EligibleAgent | null> {
    if (agents.length === 0) return null

    switch (strategy) {
      case 'round_robin':
        return this.roundRobinSelection(agents)

      case 'least_loaded':
        return this.leastLoadedSelection(agents)

      case 'weighted_round_robin':
        return this.weightedRoundRobinSelection(agents)

      case 'skill_based':
        return this.skillBasedSelection(agents)

      default:
        return this.roundRobinSelection(agents)
    }
  }

  /**
   * Round-robin selection: Choose agent who was assigned least recently
   */
  private roundRobinSelection(agents: EligibleAgent[]): EligibleAgent {
    // Sort by last assigned time (null/undefined first, then oldest first)
    const sortedAgents = agents.sort((a, b) => {
      if (!a.lastAssignedAt && !b.lastAssignedAt) return 0
      if (!a.lastAssignedAt) return -1
      if (!b.lastAssignedAt) return 1
      return a.lastAssignedAt.getTime() - b.lastAssignedAt.getTime()
    })

    return sortedAgents[0]
  }

  /**
   * Least loaded selection: Choose agent with lowest utilization
   */
  private leastLoadedSelection(agents: EligibleAgent[]): EligibleAgent {
    return agents.reduce((least, current) => 
      current.utilizationRate < least.utilizationRate ? current : least
    )
  }

  /**
   * Weighted round-robin: Consider both load and recency
   */
  private weightedRoundRobinSelection(agents: EligibleAgent[]): EligibleAgent {
    const now = new Date().getTime()

    const scoredAgents = agents.map(agent => {
      // Load factor (lower is better)
      const loadScore = 100 - agent.utilizationRate

      // Recency factor (longer since last assignment is better)
      const timeSinceAssignment = agent.lastAssignedAt
        ? now - agent.lastAssignedAt.getTime()
        : now // If never assigned, give max score

      const recencyScore = Math.min(timeSinceAssignment / (1000 * 60 * 60), 100) // Max 100 points for 1+ hours

      // Combined score (equal weight to load and recency)
      const totalScore = (loadScore * 0.6) + (recencyScore * 0.4)

      return { agent, score: totalScore }
    })

    // Sort by score (highest first) and return the best agent
    scoredAgents.sort((a, b) => b.score - a.score)
    return scoredAgents[0].agent
  }

  /**
   * Skill-based selection: Prioritize agents with best skill match
   */
  private skillBasedSelection(agents: EligibleAgent[]): EligibleAgent {
    const now = new Date().getTime()

    const scoredAgents = agents.map(agent => {
      // Skill match score (primary factor)
      const skillScore = agent.skillMatch || 100

      // Load factor (secondary)
      const loadScore = 100 - agent.utilizationRate

      // Recency factor (tertiary)
      const timeSinceAssignment = agent.lastAssignedAt
        ? now - agent.lastAssignedAt.getTime()
        : now
      const recencyScore = Math.min(timeSinceAssignment / (1000 * 60 * 60), 100)

      // Weighted score: skills (70%), load (20%), recency (10%)
      const totalScore = (skillScore * 0.7) + (loadScore * 0.2) + (recencyScore * 0.1)

      return { agent, score: totalScore }
    })

    // Sort by score (highest first) and return the best agent
    scoredAgents.sort((a, b) => b.score - a.score)
    return scoredAgents[0].agent
  }

  /**
   * Assign task to specific agent with database transaction
   */
  private async assignTaskToAgent(taskId: string, agentId: string): Promise<RoutingResult> {
    try {
      // Use transaction to ensure consistency
      const result = await prisma.$transaction(async (tx) => {
        // Double-check agent availability
        const agent = await tx.user.findUnique({
          where: { id: agentId },
          include: {
            availability: {
              orderBy: { createdAt: 'desc' },
              take: 1
            }
          }
        })

        if (!agent) {
          throw new Error('Agent not found')
        }

        if (agent.currentTaskCount >= agent.maxConcurrentTasks) {
          throw new Error('Agent capacity exceeded')
        }

        // Check availability status
        const currentAvailability = agent.availability[0]
        if (currentAvailability && currentAvailability.status !== 'available') {
          // Check if temporary status has expired
          if (currentAvailability.isTemporary && currentAvailability.endTime && new Date() > currentAvailability.endTime) {
            // Status has expired, agent is available
          } else {
            throw new Error(`Agent not available (status: ${currentAvailability.status})`)
          }
        }

        // Update task
        const updatedTask = await tx.task.update({
          where: { id: taskId },
          data: {
            assignedTo: agentId,
            assignedAt: new Date(),
            status: TaskStatus.ASSIGNED,
          },
          include: {
            assignedUser: true,
            organization: true,
          },
        })

        // Update agent task count
        await tx.user.update({
          where: { id: agentId },
          data: {
            currentTaskCount: { increment: 1 },
          },
        })

        // Create task event
        await tx.taskEvent.create({
          data: {
            taskId,
            userId: agentId,
            type: TaskEventType.ASSIGNED,
            data: {
              assignedTo: agentId,
              assignedAt: new Date(),
              routingStrategy: 'round_robin',
            },
          },
        })

        // Update metrics on task assignment
        await this.performanceManager.onTaskAssigned(taskId, agentId)

        return { task: updatedTask, agent }
      })

      return {
        success: true,
        assignedAgent: result.agent,
        reason: 'Task assigned successfully'
      }

    } catch (error) {
      console.error('Error assigning task to agent:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Assignment failed'
      }
    }
  }

  /**
   * Get routing statistics for monitoring
   */
  async getRoutingStats() {
    const [totalAgents, totalTasks, assignedTasks] = await Promise.all([
      prisma.user.count({ where: { organizationId: this.organizationId } }),
      prisma.task.count({ where: { organizationId: this.organizationId } }),
      prisma.task.count({
        where: {
          organizationId: this.organizationId,
          status: TaskStatus.ASSIGNED
        }
      }),
    ])

    // Count available agents using the new availability system
    const allAgents = await prisma.user.findMany({
      where: { organizationId: this.organizationId },
      include: {
        availability: {
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    })

    const availableAgents = allAgents.filter(agent => {
      const currentAvailability = agent.availability[0]
      if (!currentAvailability) return true // No availability record means available

      if (currentAvailability.status !== 'available') {
        // Check if temporary status has expired
        if (currentAvailability.isTemporary && currentAvailability.endTime && new Date() > currentAvailability.endTime) {
          return true // Status has expired, agent is available
        }
        return false // Agent is not available
      }
      return true // Agent is available
    }).length

    return {
      totalAgents,
      availableAgents,
      totalTasks,
      assignedTasks,
      routingCapacity: availableAgents > 0 ? 'available' : 'limited'
    }
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create a router instance for an organization
 */
export function createTaskRouter(organizationId: string): TaskRouter {
  return new TaskRouter(organizationId)
}

/**
 * Auto-assign a task using the default routing strategy
 */
export async function autoAssignTask(
  taskId: string, 
  organizationId: string,
  strategy: RoutingStrategy = 'round_robin'
): Promise<RoutingResult> {
  const router = createTaskRouter(organizationId)
  return router.routeTask(taskId, strategy)
}
