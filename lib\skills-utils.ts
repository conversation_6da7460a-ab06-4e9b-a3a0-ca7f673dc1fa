import { prisma } from './prisma';
<<<<<<< HEAD
import { AgentSkill, TaskRequiredSkill } from './types';

export class SkillsManager {
  // Agent skills management
  async addSkillToAgent(agentId: string, skillName: string, proficiencyLevel: number = 1) {
    // Validate skill exists in catalog
    const skill = await prisma.skillsCatalog.findFirst({
      where: {
        name: skillName,
        isActive: true
      }
    });
    
    if (!skill) {
      throw new Error('Skill not found in catalog');
    }

    // Add skill to agent
    return await prisma.agentSkill.create({
      data: {
        agentId,
        skillName,
        proficiencyLevel: Math.min(Math.max(proficiencyLevel, 1), 5) // Ensure 1-5 range
      }
    });
  }
  
  async removeSkillFromAgent(agentId: string, skillName: string) {
    return await prisma.agentSkill.delete({
      where: {
        agentId_skillName: {
          agentId,
          skillName
        }
      }
    });
  }
  
  async updateAgentSkillLevel(agentId: string, skillName: string, proficiencyLevel: number) {
    return await prisma.agentSkill.update({
      where: {
        agentId_skillName: {
          agentId,
          skillName
        }
      },
      data: {
        proficiencyLevel: Math.min(Math.max(proficiencyLevel, 1), 5)
      }
    });
  }
  
  async getAgentSkills(agentId: string) {
    return await prisma.agentSkill.findMany({
      where: { agentId }
    });
  }
  
  // Task skills management
  async addSkillRequirementToTask(taskId: string, skillName: string, requiredLevel: number = 1) {
    // Validate skill exists in catalog
    const skill = await prisma.skillsCatalog.findFirst({
      where: {
        name: skillName,
        isActive: true
      }
    });
    
    if (!skill) {
      throw new Error('Skill not found in catalog');
    }

    return await prisma.taskRequiredSkill.create({
      data: {
        taskId,
        skillName,
        requiredLevel: Math.min(Math.max(requiredLevel, 1), 5)
      }
    });
  }
  
  async removeSkillRequirementFromTask(taskId: string, skillName: string) {
    return await prisma.taskRequiredSkill.delete({
      where: {
        taskId_skillName: {
          taskId,
          skillName
        }
      }
    });
  }
  
  async getTaskSkillRequirements(taskId: string) {
    return await prisma.taskRequiredSkill.findMany({
      where: { taskId }
    });
  }
  
  // Skills catalog management
  async createSkill(organizationId: string, name: string, description?: string, category?: string) {
    // Check if skill already exists
    const existing = await prisma.skillsCatalog.findUnique({
      where: {
        organizationId_name: {
          organizationId,
          name
        }
      }
    });
    
    if (existing) {
      throw new Error('Skill already exists in catalog');
    }

    return await prisma.skillsCatalog.create({
      data: {
        organizationId,
        name,
        description,
        category,
        isActive: true
      }
    });
  }
  
  async getOrganizationSkills(organizationId: string) {
    return await prisma.skillsCatalog.findMany({
      where: {
        organizationId,
        isActive: true
      }
    });
  }
  
  // Skill matching utilities
  async findAgentsWithSkills(organizationId: string, requiredSkills: string[]) {
    return await prisma.user.findMany({
      where: {
        organizationId,
        role: 'AGENT',
        skills: {
          some: {
            skillName: {
              in: requiredSkills
            }
          }
        }
      },
      include: {
        skills: true
      }
    });
  }
  
  async calculateSkillMatch(agentSkills: AgentSkill[], requiredSkills: TaskRequiredSkill[]): Promise<number> {
    if (requiredSkills.length === 0) return 1.0; // No requirements = perfect match
    
    let totalMatch = 0;
    let totalRequired = 0;
=======
import {
  AgentSkill,
  TaskRequiredSkill,
  SkillsCatalog,
  AgentSkillMatch,
  SkillMatch,
  UserWithRelations,
  CreateSkillRequest,
} from './types';

/**
 * Skills Management Utility Class
 * Handles all skills-related operations for agents, tasks, and organization catalog
 */
export class SkillsManager {
  
  // ============================================================================
  // AGENT SKILLS MANAGEMENT
  // ============================================================================

  /**
   * Add a skill to an agent
   */
  async addSkillToAgent(agentId: string, skillName: string, proficiencyLevel: number = 1): Promise<AgentSkill> {
    // Validate proficiency level
    if (proficiencyLevel < 1 || proficiencyLevel > 5) {
      throw new Error('Proficiency level must be between 1 and 5');
    }

    try {
      const skill = await prisma.agentSkill.create({
        data: {
          agentId,
          skillName,
          proficiencyLevel,
        },
      });
      return skill;
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'code' in error && error.code === 'P2002') {
        throw new Error(`Agent already has skill: ${skillName}`);
      }
      throw error;
    }
  }

  /**
   * Remove a skill from an agent
   */
  async removeSkillFromAgent(agentId: string, skillName: string): Promise<boolean> {
    const result = await prisma.agentSkill.deleteMany({
      where: {
        agentId,
        skillName,
      },
    });
    return result.count > 0;
  }

  /**
   * Update an agent's skill proficiency level
   */
  async updateAgentSkillLevel(agentId: string, skillName: string, proficiencyLevel: number): Promise<AgentSkill> {
    // Validate proficiency level
    if (proficiencyLevel < 1 || proficiencyLevel > 5) {
      throw new Error('Proficiency level must be between 1 and 5');
    }

    const skill = await prisma.agentSkill.update({
      where: {
        agentId_skillName: {
          agentId,
          skillName,
        },
      },
      data: {
        proficiencyLevel,
      },
    });
    return skill;
  }

  /**
   * Get all skills for an agent
   */
  async getAgentSkills(agentId: string): Promise<AgentSkill[]> {
    return prisma.agentSkill.findMany({
      where: { agentId },
      orderBy: { skillName: 'asc' },
    });
  }

  // ============================================================================
  // TASK SKILLS MANAGEMENT
  // ============================================================================

  /**
   * Add a skill requirement to a task
   */
  async addSkillRequirementToTask(taskId: string, skillName: string, requiredLevel: number = 1): Promise<TaskRequiredSkill> {
    // Validate required level
    if (requiredLevel < 1 || requiredLevel > 5) {
      throw new Error('Required level must be between 1 and 5');
    }

    try {
      const requirement = await prisma.taskRequiredSkill.create({
        data: {
          taskId,
          skillName,
          requiredLevel,
        },
      });
      return requirement;
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'code' in error && error.code === 'P2002') {
        throw new Error(`Task already requires skill: ${skillName}`);
      }
      throw error;
    }
  }

  /**
   * Remove a skill requirement from a task
   */
  async removeSkillRequirementFromTask(taskId: string, skillName: string): Promise<boolean> {
    const result = await prisma.taskRequiredSkill.deleteMany({
      where: {
        taskId,
        skillName,
      },
    });
    return result.count > 0;
  }

  /**
   * Get all skill requirements for a task
   */
  async getTaskSkillRequirements(taskId: string): Promise<TaskRequiredSkill[]> {
    return prisma.taskRequiredSkill.findMany({
      where: { taskId },
      orderBy: { skillName: 'asc' },
    });
  }

  // ============================================================================
  // SKILLS CATALOG MANAGEMENT
  // ============================================================================

  /**
   * Create a new skill in the organization catalog
   */
  async createSkill(organizationId: string, skillData: CreateSkillRequest): Promise<SkillsCatalog> {
    try {
      const skill = await prisma.skillsCatalog.create({
        data: {
          organizationId,
          name: skillData.name,
          description: skillData.description,
          category: skillData.category,
        },
      });
      return skill;
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'code' in error && error.code === 'P2002') {
        throw new Error(`Skill already exists: ${skillData.name}`);
      }
      throw error;
    }
  }

  /**
   * Get all skills in organization catalog
   */
  async getOrganizationSkills(organizationId: string, activeOnly: boolean = true): Promise<SkillsCatalog[]> {
    return prisma.skillsCatalog.findMany({
      where: {
        organizationId,
        ...(activeOnly && { isActive: true }),
      },
      orderBy: [
        { category: 'asc' },
        { name: 'asc' },
      ],
    });
  }

  /**
   * Deactivate a skill in the catalog
   */
  async deactivateSkill(skillId: string): Promise<SkillsCatalog> {
    return prisma.skillsCatalog.update({
      where: { id: skillId },
      data: { isActive: false },
    });
  }

  // ============================================================================
  // SKILL MATCHING UTILITIES
  // ============================================================================

  /**
   * Find agents with specific skills
   */
  async findAgentsWithSkills(organizationId: string, requiredSkills: string[]): Promise<UserWithRelations[]> {
    return prisma.user.findMany({
      where: {
        organizationId,
        skills: {
          some: {
            skillName: {
              in: requiredSkills,
            },
          },
        },
      },
      include: {
        organization: true,
        skills: true,
        _count: {
          select: {
            assignedTasks: true,
            taskEvents: true
          },
        },
      },
    });
  }

  /**
   * Calculate skill match percentage between agent skills and task requirements
   */
  async calculateSkillMatch(agentSkills: AgentSkill[], requiredSkills: TaskRequiredSkill[]): Promise<{
    overallMatch: number;
    skillMatches: SkillMatch[];
    missingSkills: string[];
  }> {
    const skillMatches: SkillMatch[] = [];
    const missingSkills: string[] = [];
    
    const totalRequired = requiredSkills.length;
    let matchedSkills = 0;
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
    
    for (const required of requiredSkills) {
      const agentSkill = agentSkills.find(s => s.skillName === required.skillName);
      
      if (agentSkill) {
<<<<<<< HEAD
        // Calculate match quality based on proficiency vs requirement
        const matchQuality = Math.min(agentSkill.proficiencyLevel / required.requiredLevel, 1.0);
        totalMatch += matchQuality;
      }
      totalRequired += 1;
    }
    
    return totalMatch / totalRequired; // Returns 0-1 score
  }
}
=======
        const match = agentSkill.proficiencyLevel >= required.requiredLevel;
        skillMatches.push({
          skillName: required.skillName,
          required: required.requiredLevel,
          actual: agentSkill.proficiencyLevel,
          match,
        });
        
        if (match) {
          matchedSkills++;
        }
      } else {
        skillMatches.push({
          skillName: required.skillName,
          required: required.requiredLevel,
          actual: 0,
          match: false,
        });
        missingSkills.push(required.skillName);
      }
    }
    
    const overallMatch = totalRequired > 0 ? (matchedSkills / totalRequired) * 100 : 100;
    
    return {
      overallMatch,
      skillMatches,
      missingSkills,
    };
  }

  /**
   * Get agents ranked by skill match for a specific task
   */
  async getAgentSkillMatches(taskId: string): Promise<AgentSkillMatch[]> {
    // Get task requirements
    const taskRequirements = await this.getTaskSkillRequirements(taskId);
    
    if (taskRequirements.length === 0) {
      // If no skills required, return all available agents
      const agents = await prisma.user.findMany({
        where: {
          status: 'AVAILABLE',
        },
        include: {
          organization: true,
          skills: true,
          _count: {
            select: {
              assignedTasks: true,
              taskEvents: true
            },
          },
        },
      });
      
      return agents.map(agent => ({
        agent,
        skillMatches: [],
        overallMatch: 100,
        missingSkills: [],
      }));
    }
    
    // Get task organization
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      select: { organizationId: true },
    });
    
    if (!task) {
      throw new Error('Task not found');
    }
    
    // Get all agents in organization with their skills
    const agents = await prisma.user.findMany({
      where: {
        organizationId: task.organizationId,
        status: 'AVAILABLE',
      },
      include: {
        organization: true,
        skills: true,
        _count: {
          select: {
            assignedTasks: true,
            taskEvents: true
          },
        },
      },
    });
    
    // Calculate matches for each agent
    const matches: AgentSkillMatch[] = [];
    
    for (const agent of agents) {
      const matchResult = await this.calculateSkillMatch(agent.skills, taskRequirements);
      matches.push({
        agent,
        skillMatches: matchResult.skillMatches,
        overallMatch: matchResult.overallMatch,
        missingSkills: matchResult.missingSkills,
      });
    }
    
    // Sort by match percentage (highest first)
    return matches.sort((a, b) => b.overallMatch - a.overallMatch);
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create a skills manager instance
 */
export function createSkillsManager(): SkillsManager {
  return new SkillsManager();
}

/**
 * Get the best matching agents for a task based on skills
 */
export async function getBestMatchingAgents(taskId: string, limit: number = 5): Promise<AgentSkillMatch[]> {
  const skillsManager = createSkillsManager();
  const matches = await skillsManager.getAgentSkillMatches(taskId);
  return matches.slice(0, limit);
}
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
