import {
  Organization,
  User as PrismaUser,
  Task as PrismaTask,
  UserStatus,
  UserRole,
  TaskStatus,
  TaskPriority,
  TaskEventType,
<<<<<<< HEAD
  Prisma
=======
  AgentSkill,
  TaskRequiredSkill,
  SkillsCatalog,
  AgentWorkingHours,
  AgentAvailability,
  OrganizationSettings,
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
} from "@prisma/client";

export type JsonValue = Prisma.JsonValue;

// Re-export enums and basic types
export { UserStatus, UserRole, TaskStatus, TaskPriority, TaskEventType };
export type { Organization };

<<<<<<< HEAD
export interface Escalation {
  id: string;
  taskId: string;
  level: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface SLAPolicy {
=======
export type UserWithRelations = User & {
  organization?: Organization;
  assignedTasks?: Task[];
  taskEvents?: TaskEvent[];
  _count?: {
    assignedTasks: number;
    taskEvents: number;
  };
};

export type TaskWithRelations = Task & {
  organization?: Organization;
  assignedUser?: User | null;
  events?: TaskEvent[];
  _count?: {
    events: number;
  };
};

export type TaskEventWithRelations = TaskEvent & {
  task?: Task;
  user?: User | null;
};

// ============================================================================
// API TYPES
// ============================================================================

export interface CreateTaskRequest {
  title: string;
  description?: string;
  priority?: TaskPriority;
  type?: string;
  estimatedDuration?: number;
  source?: string;
  responseDeadline?: Date; // Will be mapped to response_by in db-utils
  slaDeadline?: Date; // Will be mapped to resolve_by in db-utils
}

export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  priority?: TaskPriority;
  type?: string;
  estimatedDuration?: number;
  status?: TaskStatus;
  assignedTo?: string | null;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  password?: string;
  role?: UserRole;
  maxConcurrentTasks?: number;
  status?: UserStatus;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  role?: UserRole;
  maxConcurrentTasks?: number;
  status?: UserStatus;
}

export interface CreateOrganizationRequest {
  name: string;
}

// ============================================================================
// AUTHENTICATION TYPES
// ============================================================================

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  organizationId?: string;
  role?: UserRole;
}

export interface AuthUser {
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
  id: string;
  organizationId: string;
  name: string;
  description?: string | null;
  priority: string;
  responseTime: number;
  resolutionTime: number;
  escalationRules: JsonValue;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export type ParsedSLAPolicy = Omit<SLAPolicy, 'escalationRules'> & {
  escalationRules: EscalationRule[];
};

export interface EscalationRule {
  level: number;
  escalateTo?: string;
  actions: EscalationAction[];
}

export interface EscalationAction {
  type: 'notify' | 'reassign' | 'update_priority';
  assignTo?: string;
  priority?: TaskPriority;
  notifyUsers?: string[];
}

export type MetricPeriod = 'daily' | 'weekly' | 'monthly';

export interface TaskMetrics {
  taskId: string;
  waitTime: number | null;
  handleTime: number | null;
  totalTime: number | null;
  metResponseSLA: boolean | null;
  metResolveSLA: boolean | null;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AgentMetrics {
  agentId: string;
  period: MetricPeriod;
  startDate: Date;
  endDate: Date;
  tasksAssigned: number;
  tasksCompleted: number;
  tasksEscalated: number;
  avgResponseTime: number;
  avgHandleTime: number;
  completionRate: number;
  slaComplianceRate: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TeamMetrics {
  organizationId: string;
  period: MetricPeriod;
  startDate: Date;
  endDate: Date;
  totalTasks: number;
  completedTasks: number;
  escalatedTasks: number;
  avgResponseTime: number;
  avgHandleTime: number;
  slaComplianceRate: number;
  activeAgents: number;
  utilizationRate: number;
  createdAt?: Date;
  updatedAt?: Date;
}

// Extended Prisma Types
export interface Task extends Omit<PrismaTask, 'assignedAt' | 'firstResponseAt' | 'resolvedAt'> {
  assignedAt: Date | null;
  firstResponseAt?: Date | null;
  resolvedAt: Date | null;
  slaPolicy?: SLAPolicy | null;
  metrics?: TaskMetrics | null;
  escalations: Escalation[];
}

export interface ExtendedUser extends Omit<PrismaUser, 'status'> {
  maxConcurrentTasks: number;
  status: UserStatus;
}

export type User = ExtendedUser;

<<<<<<< HEAD
// Type Guards and Helpers
export const isTaskWithMetrics = (task: Task): task is Task & { metrics: NonNullable<TaskMetrics> } => {
  return task && task.metrics !== null && task.metrics !== undefined;
};

export const isAvailableStatus = (status: UserStatus): boolean => {
  return status === 'AVAILABLE';
};

export const isCompletedStatus = (status: TaskStatus): boolean => {
  return status === 'COMPLETED';
};

export const isValidMetricPeriod = (value: string): value is MetricPeriod => {
  return ['daily', 'weekly', 'monthly'].includes(value);
=======
// ============================================================================
// ROUTING TYPES (for future phases)
// ============================================================================

export interface Assignment {
  taskId: string;
  agentId: string;
  assignedAt: Date;
  confidence: number;
}

export type RoutingStrategy =
  | "round_robin"
  | "weighted_round_robin"
  | "least_loaded"
  | "best_match";

// ============================================================================
// FILTER TYPES
// ============================================================================

export interface TaskFilters {
  status?: TaskStatus[];
  priority?: TaskPriority[];
  type?: string[];
  assignedTo?: string[];
  dateRange?: {
    from: Date;
    to: Date;
  };
}

export interface UserFilters {
  status?: UserStatus[];
  organizationId?: string;
}

// ============================================================================
// PAGINATION TYPES
// ============================================================================

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// ============================================================================
// RE-EXPORT PRISMA ENUMS
// ============================================================================

export {
  UserStatus,
  UserRole,
  TaskStatus,
  TaskPriority,
  TaskEventType,
  type Organization,
  type User,
  type Task,
  type TaskEvent,
  type AgentSkill,
  type TaskRequiredSkill,
  type SkillsCatalog,
  type AgentWorkingHours,
  type AgentAvailability,
  type OrganizationSettings,
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
};

// ============================================================================
// SKILLS MANAGEMENT TYPES (Phase 2)
// ============================================================================

export interface AgentWithSkills extends User {
  skills: AgentSkill[];
}

export interface TaskWithSkills extends Task {
  requiredSkills: TaskRequiredSkill[];
}

// ============================================================================
// WORKING HOURS & AVAILABILITY TYPES (Phase 2 - Task 3)
// ============================================================================

export interface WorkingHours {
  id: string;
  agentId: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  timezone: string;
  isActive: boolean;
}

export interface AvailabilityStatus {
  id: string;
  agentId: string;
  status: 'available' | 'busy' | 'away' | 'offline';
  reason?: string;
  startTime?: Date;
  endTime?: Date;
  isTemporary: boolean;
}

export interface OrganizationWorkingSettings {
  id: string;
  organizationId: string;
  defaultTimezone: string;
  businessHoursStart: string;
  businessHoursEnd: string;
  businessDays: number[];
  afterHoursRouting: boolean;
  weekendRouting: boolean;
  urgentTasksOverride: boolean;
  overrideAgentHours: boolean;
}

export interface AgentWithWorkingHours extends User {
  working_hours: AgentWorkingHours[];
  availability: AgentAvailability[];
}

export interface SetWorkingHoursRequest {
  schedule: {
    dayOfWeek: number;
    startTime: string;
    endTime: string;
    timezone?: string;
    isActive?: boolean;
  }[];
}

export interface SetAvailabilityRequest {
  status: 'available' | 'busy' | 'away' | 'offline';
  reason?: string;
  duration?: number; // minutes
}

export interface AvailabilityCheckRequest {
  agentIds: string[];
  checkTime?: string; // ISO string
}

export interface AvailabilityCheckResponse {
  agentId: string;
  isAvailable: boolean;
  reason?: string;
  nextAvailableTime?: string; // ISO string
}

export interface OrganizationWithSkills extends Organization {
  skillsCatalog: SkillsCatalog[];
}

export interface SkillMatch {
  skillName: string;
  required: number;
  actual: number;
  match: boolean;
}

export interface AgentSkillMatch {
  agent: UserWithRelations;
  skillMatches: SkillMatch[];
  overallMatch: number; // percentage 0-100
  missingSkills: string[];
}

export interface SkillRequirement {
  skillName: string;
  requiredLevel: number;
}

export interface CreateSkillRequest {
  name: string;
  description?: string;
  category?: string;
}

export interface AddAgentSkillRequest {
  skillName: string;
  proficiencyLevel: number;
}

export interface AddTaskSkillRequest {
  skillName: string;
  requiredLevel: number;
}

// ============================================================================
// ENHANCED ROUTING TYPES (Phase 2 - Task 2)
// ============================================================================

export interface RoutingContext {
  task: TaskWithSkills;
  availableAgents: AgentWithSkills[];
  organizationId: string;
  strategy?: string;
  urgencyMultiplier?: number;
}

export interface RoutingResult {
  selectedAgent: AgentWithSkills;
  confidence: number; // 0-100
  reasoning: string[];
  alternativeAgents: AgentWithSkills[];
}

export interface AgentWithPerformance extends AgentWithSkills {
  performance?: {
    avgResponseTime: number;
    avgResolutionTime: number;
    completionRate: number;
    qualityScore: number;
    totalTasksCompleted: number;
    totalTasksAssigned: number;
    lastUpdated: Date;
  };
}

export interface ScoredAgent {
  agent: AgentWithSkills;
  score: number;
  breakdown: {
    skillScore: number;
    workloadScore: number;
    performanceScore: number;
    urgencyScore?: number;
  };
}

export type EnhancedRoutingStrategy =
  | 'weighted_round_robin'
  | 'best_skill_match'
  | 'performance_based'
  | 'hybrid_intelligent';

export interface RoutingStrategyConfig {
  id: string;
  organizationId: string;
  name: string;
  strategy: EnhancedRoutingStrategy;
  isDefault: boolean;
  isActive: boolean;
  configuration: Record<string, unknown>;
  createdAt: Date;
}
