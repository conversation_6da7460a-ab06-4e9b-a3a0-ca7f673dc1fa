import { NextRequest, NextResponse } from 'next/server';
<<<<<<< HEAD
import { SkillsManager } from '@/lib/skills-utils';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

const skillsManager = new SkillsManager();

interface Props {
  params: { id: string }
}

export async function GET(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const taskId = params.id;
    
    // Get task skill requirements
    const skills = await skillsManager.getTaskSkillRequirements(taskId);

    return NextResponse.json({ skills });
  } catch (error) {
    console.error('Error in GET /api/tasks/[id]/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
=======
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createSkillsManager } from '@/lib/skills-utils';
import { AddTaskSkillRequest } from '@/lib/types';

/**
 * GET /api/tasks/[id]/skills
 * Get all skill requirements for a specific task
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const taskId = params.id;

    const skillsManager = createSkillsManager();
    const requirements = await skillsManager.getTaskSkillRequirements(taskId);

    return NextResponse.json({
      success: true,
      data: requirements,
    });
  } catch (error) {
    console.error('Error fetching task skill requirements:', error);
    return NextResponse.json(
      { error: 'Failed to fetch task skill requirements' },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
      { status: 500 }
    );
  }
}

<<<<<<< HEAD
export async function POST(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const taskId = params.id;
    
    // Parse request body
    const body = await req.json();
    const { skillName, requiredLevel } = body;

    if (!skillName) {
=======
/**
 * POST /api/tasks/[id]/skills
 * Add a skill requirement to a task
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const taskId = params.id;

    // Only admins can add skill requirements to tasks
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body: AddTaskSkillRequest = await request.json();

    // Validate required fields
    if (!body.skillName || body.skillName.trim().length === 0) {
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

<<<<<<< HEAD
    // Add skill requirement to task
    const skill = await skillsManager.addSkillRequirementToTask(
      taskId,
      skillName,
      requiredLevel
    );

    return NextResponse.json(
      { skill },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/tasks/[id]/skills:', error);
    
    // Handle known errors
    if (error instanceof Error) {
      if (error.message === 'Skill not found in catalog') {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const taskId = params.id;
    
    // Parse request body
    const body = await req.json();
    const { skillName, requiredLevel } = body;

    if (!skillName || requiredLevel === undefined) {
      return NextResponse.json(
        { error: 'Skill name and required level are required' },
=======
    if (!body.requiredLevel || body.requiredLevel < 1 || body.requiredLevel > 5) {
      return NextResponse.json(
        { error: 'Required level must be between 1 and 5' },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
        { status: 400 }
      );
    }

<<<<<<< HEAD
    // Update skill requirement
    const updatedSkill = await prisma.taskRequiredSkill.update({
      where: {
        taskId_skillName: {
          taskId,
          skillName
        }
      },
      data: {
        requiredLevel: Math.min(Math.max(requiredLevel, 1), 5) // Ensure 1-5 range
      }
    });

    return NextResponse.json({ skill: updatedSkill });
  } catch (error) {
    console.error('Error in PATCH /api/tasks/[id]/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
=======
    const skillsManager = createSkillsManager();
    const requirement = await skillsManager.addSkillRequirementToTask(
      taskId,
      body.skillName.trim(),
      body.requiredLevel
    );

    return NextResponse.json({
      success: true,
      data: requirement,
    }, { status: 201 });
  } catch (error: unknown) {
    console.error('Error adding task skill requirement:', error);

    if (error instanceof Error && error.message.includes('already requires skill')) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to add skill requirement to task' },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
      { status: 500 }
    );
  }
}

<<<<<<< HEAD
export async function DELETE(req: NextRequest, { params }: Props) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const taskId = params.id;
    
    // Get skill name from query params
    const { searchParams } = new URL(req.url);
=======
/**
 * PUT /api/tasks/[id]/skills
 * Update a task's skill requirement level
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const taskId = params.id;

    // Only admins can update task skill requirements
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { skillName, requiredLevel } = body;

    // Validate required fields
    if (!skillName || skillName.trim().length === 0) {
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

    if (!requiredLevel || requiredLevel < 1 || requiredLevel > 5) {
      return NextResponse.json(
        { error: 'Required level must be between 1 and 5' },
        { status: 400 }
      );
    }

    // For now, we'll implement this by removing and re-adding
    // In a real implementation, you'd add an updateTaskSkillRequirement method
    const skillsManager = createSkillsManager();
    
    // Remove existing requirement
    await skillsManager.removeSkillRequirementFromTask(taskId, skillName.trim());
    
    // Add new requirement with updated level
    const requirement = await skillsManager.addSkillRequirementToTask(
      taskId,
      skillName.trim(),
      requiredLevel
    );

    return NextResponse.json({
      success: true,
      data: requirement,
    });
  } catch (error: unknown) {
    console.error('Error updating task skill requirement:', error);
    return NextResponse.json(
      { error: 'Failed to update task skill requirement' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/tasks/[id]/skills
 * Remove a skill requirement from a task
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const taskId = params.id;

    // Only admins can remove task skill requirements
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
    const skillName = searchParams.get('skillName');

    if (!skillName) {
      return NextResponse.json(
        { error: 'Skill name is required' },
        { status: 400 }
      );
    }

<<<<<<< HEAD
    // Remove skill requirement from task
    await skillsManager.removeSkillRequirementFromTask(taskId, skillName);

    return NextResponse.json(
      { message: 'Skill requirement removed successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in DELETE /api/tasks/[id]/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
=======
    const skillsManager = createSkillsManager();
    const removed = await skillsManager.removeSkillRequirementFromTask(taskId, skillName);

    if (!removed) {
      return NextResponse.json(
        { error: 'Skill requirement not found for this task' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Skill requirement removed successfully',
    });
  } catch (error) {
    console.error('Error removing task skill requirement:', error);
    return NextResponse.json(
      { error: 'Failed to remove task skill requirement' },
      { status: 500 }
    );
  }
}


>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
