import { NextRequest, NextResponse } from 'next/server';
<<<<<<< HEAD
import { prisma } from '../../../lib/prisma';
import { SkillsManager } from '../../../lib/skills-utils';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// Initialize skills manager
const skillsManager = new SkillsManager();

export async function GET(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get organizationId from query params
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get('organizationId');

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    // Get organization skills
    const skills = await skillsManager.getOrganizationSkills(organizationId);

    return NextResponse.json({ skills });
  } catch (error) {
    console.error('Error in GET /api/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
=======
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createSkillsManager } from '@/lib/skills-utils';
import { CreateSkillRequest } from '@/lib/types';

/**
 * GET /api/skills
 * Get all skills in the organization catalog
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const activeOnly = searchParams.get('active') !== 'false';
    const category = searchParams.get('category');

    const skillsManager = createSkillsManager();
    let skills = await skillsManager.getOrganizationSkills(
      session.user.organizationId,
      activeOnly
    );

    // Filter by category if specified
    if (category) {
      skills = skills.filter(skill => skill.category === category);
    }

    return NextResponse.json({
      success: true,
      data: skills,
    });
  } catch (error) {
    console.error('Error fetching skills:', error);
    return NextResponse.json(
      { error: 'Failed to fetch skills' },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
      { status: 500 }
    );
  }
}

<<<<<<< HEAD
export async function POST(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { organizationId, name, description, category } = body;

    // Validate required fields
    if (!organizationId || !name) {
      return NextResponse.json(
        { error: 'Organization ID and skill name are required' },
=======
/**
 * POST /api/skills
 * Create a new skill in the organization catalog
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can create skills
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body: CreateSkillRequest = await request.json();

    // Validate required fields
    if (!body.name || body.name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Skill name is required' },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
        { status: 400 }
      );
    }

<<<<<<< HEAD
    // Create new skill
    const skill = await skillsManager.createSkill(
      organizationId,
      name,
      description,
      category
    );

    return NextResponse.json(
      { skill },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in POST /api/skills:', error);
    
    // Handle known errors
    if (error instanceof Error && error.message === 'Skill already exists in catalog') {
=======
    const skillsManager = createSkillsManager();
    const skill = await skillsManager.createSkill(
      session.user.organizationId,
      {
        name: body.name.trim(),
        description: body.description?.trim(),
        category: body.category?.trim(),
      }
    );

    return NextResponse.json({
      success: true,
      data: skill,
    }, { status: 201 });
  } catch (error: unknown) {
    console.error('Error creating skill:', error);

    if (error instanceof Error && error.message.includes('already exists')) {
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
<<<<<<< HEAD
      { error: 'Internal server error' },
=======
      { error: 'Failed to create skill' },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
      { status: 500 }
    );
  }
}

<<<<<<< HEAD
export async function PATCH(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    const { id, isActive, description, category } = body;

    if (!id) {
=======
/**
 * PUT /api/skills/[id]
 * Update a skill in the catalog
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can update skills
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const skillId = searchParams.get('id');

    if (!skillId) {
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
      return NextResponse.json(
        { error: 'Skill ID is required' },
        { status: 400 }
      );
    }

<<<<<<< HEAD
    // Update skill
    const updatedSkill = await prisma.skillsCatalog.update({
      where: { id },
      data: {
        isActive: isActive !== undefined ? isActive : undefined,
        description: description !== undefined ? description : undefined,
        category: category !== undefined ? category : undefined
      }
    });

    return NextResponse.json({ skill: updatedSkill });
  } catch (error) {
    console.error('Error in PATCH /api/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
=======
    const body = await request.json();
    const { action } = body;

    const skillsManager = createSkillsManager();

    if (action === 'deactivate') {
      const skill = await skillsManager.deactivateSkill(skillId);
      return NextResponse.json({
        success: true,
        data: skill,
      });
    }

    // For other updates, you would implement updateSkill method
    return NextResponse.json(
      { error: 'Update action not implemented yet' },
      { status: 501 }
    );
  } catch (error) {
    console.error('Error updating skill:', error);
    return NextResponse.json(
      { error: 'Failed to update skill' },
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
      { status: 500 }
    );
  }
}
<<<<<<< HEAD

export async function DELETE(req: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get skill ID from query params
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Skill ID is required' },
        { status: 400 }
      );
    }

    // Delete skill (this will cascade to related records due to our schema setup)
    await prisma.skillsCatalog.delete({
      where: { id }
    });

    return NextResponse.json(
      { message: 'Skill deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in DELETE /api/skills:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
=======
>>>>>>> ab5a9afed8404ef97d6c941a937024e86faf4a8a
